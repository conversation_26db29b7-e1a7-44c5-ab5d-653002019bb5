<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    destroy-on-close
    @submit="submitForm"
    @cancel="visible = false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort" required>
            <el-input-number
              v-model="form.sort"
              controls-position="right"
              placeholder=""
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" :rows="2" v-model="form.remark" placeholder="" />
          </el-form-item>
        </el-col>

        <el-col :span="20" :offset="2">
          <el-card>
            <template #header></template>
            <div>
              <el-form-item label="菜单权限" prop="menuIds">
                <el-tree
                  ref="menuTree1"
                  :data="menuTree1"
                  show-checkbox
                  check-strictly
                  node-key="id"
                  :props="{ label: 'title', children: 'children' }"
                />
              </el-form-item>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import * as roleApi from '@/api/system/role'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        roleName: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
        // menuIds: [
        //   { required: true, message: '不能为空', trigger: 'blur' },
        // ],
      },
      menuTree1: [],
    }
  },

  methods: {
    async add() {
      this.reset()
      this.getMenuTreeData()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      await this.getMenuTreeData()
      const res = await roleApi.getDetail(row.id)
      if (res.code == 0) {
        this.$xloading.hide()
        this.form = res.data

        if (this.$refs.menuTree1 != undefined) {
          this.$refs.menuTree1.setCheckedKeys(res.data.menuIds)
        }
      }
    },

    reset() {
      this.form = {}
      // this.menuTree = []
      this.$xResetForm('form')
    },

    submitForm() {
      this.form.menuIds = [...this.$refs.menuTree1.getCheckedKeys()]
      this.$refs['form'].validate(async (valid) => {
        console.log('submitForm valid: ', valid, 'data: ', Object.assign({}, this.form))

        if (valid) {
          this.$xloading.show()
          const data = this.form
          const { id } = this.form
          if (id != undefined) {
            const res = await roleApi.editRole(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await roleApi.addRole(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    async getMenuTreeData() {
      const res = await roleApi.getRoleMenuTree(0)
      if (res.code == 0) {
        this.menuTree1 = res.data
      }
    },
  },
}
</script>

<style></style>
