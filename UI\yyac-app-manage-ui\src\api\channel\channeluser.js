import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/channelUser/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/channelUser/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addchannelUser(data) {
  return request({
    url: '/channelUser/add',
    method: 'post',
    data: data,
  })
}

export function editchannelUser(data) {
  return request({
    url: '/channelUser/edit',
    method: 'post',
    data: data,
  })
}

export function delchannelUser(id) {
  return request({
    url: '/channelUser/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function resetPassword(id) {
  return request({
    url: '/channelUser/resetPassword',
    method: 'post',
    data: {
      id,
    },
  })
}

export function customerLogin(userName) {
  return request({
    url: '/channelUser/customerLogin',
    method: 'post',
    data: {
      userName,
    },
  })
}
