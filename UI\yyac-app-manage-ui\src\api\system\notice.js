import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/system/notice/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/system/notice/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addSystemNotice(data) {
  return request({
    url: '/system/notice/add',
    method: 'post',
    data: data,
  })
}

export function editSystemNotice(data) {
  return request({
    url: '/system/notice/edit',
    method: 'post',
    data: data,
  })
}

export function delSystemNotice(id) {
  return request({
    url: '/system/notice/delete',
    method: 'post',
    data: {
      id,
    },
  })
}
