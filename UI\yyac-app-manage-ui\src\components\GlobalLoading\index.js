import Vue from 'vue'
import GlobalSpinner from '@/components/GlobalLoading/index.vue'

let instance

function createInstance() {
  const vnode = new Vue({
    render: (h) => h(GlobalSpinner),
  }).$mount()
  document.body.appendChild(vnode.$el)
  return vnode.$children[0]
}

function show() {
  if (!instance) {
    instance = createInstance()
  }
  instance.show()
  return instance
}

function hide() {
  if (!instance) {
    return
  }
  instance.close()
  return instance
}

export default {
  show,
  hide,
}
