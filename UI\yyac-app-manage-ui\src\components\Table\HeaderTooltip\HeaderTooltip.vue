<template>
  <div class="layout">
    <span>{{ label }}</span>
    <el-tooltip>
      <template v-slot:content>
        <slot name="tip">{{ tip }}</slot>
      </template>
      <i class="el-icon-question"></i>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    label: String,
    tip: String,
  },
}
</script>
<style lang="scss" scoped>
.layout {
  display: inline-block;
}
</style>
