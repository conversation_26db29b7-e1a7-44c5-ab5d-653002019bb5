(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7290b6ea"],{"045c":function(e,t,r){},"0a062":function(e,t,r){"use strict";r("045c")},"1e4b":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row")],1)},c=[],a=(r("8e6e"),r("ac6a"),r("456d"),r("ade3")),o=r("2f62");function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){Object(a["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var i={name:"Dashboard",data:function(){return{cardCol:{xs:{span:24},sm:{span:12},md:{span:8}},quickStartCol:{xs:{span:12},sm:{span:8},xl:{span:6}}}},computed:p({},Object(o["c"])(["name","minaType"])),methods:{quickStart:function(e){e.href&&this.$router.push({path:e.href})}}},u=i,b=(r("0a062"),r("2877")),f=Object(b["a"])(u,n,c,!1,null,"9a65b0bc",null);t["default"]=f.exports}}]);