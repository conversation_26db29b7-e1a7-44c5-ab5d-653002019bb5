import request from '@/utils/request'

export function getChannelStatsList(params) {
  return request({
    url: '/stats/channelDailyList',
    method: 'get',
    params,
  })
}

export function getChannelCustomStatsList(params) {
    return request({
      url: '/stats/channelCustomDailyList',
      method: 'get',
      params,
    })
  }

export function getChannelCustomRetainList(params) {
  return request({
    url: '/stats/channelCustomRetain',
    method: 'get',
    params,
  })
}

export function getChannelRetainList(params) {
  return request({
    url: '/stats/channelRetain',
    method: 'get',
    params,
  })
}

export function calcRetain(data) {
  return request({
    url: '/stats/calcRetain',
    method: 'post',
    data,
  })
}
