@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// bg
.bg-white {
  background-color: #fff;
}

// main-container global css
.app-container {
  padding: 20px;
  min-height: calc(100vh - 50px);
  // background-color: #F2F6FC;
  background-color: #f5f7f9;
}

.table-toolbar {
  margin-bottom: 15px;
}

.el-form-item__label .extra {
  font-weight: normal;
  font-size: 14px;
  margin-left: 15px;
  color: #C0C4CC;
}

.el-form-item__content .extra {
  margin-left: 15px;
  color: #C0C4CC;
}

.el-form-item__content .extra.line {
  margin-left: 15px;
  color: #C0C4CC;
  display: block;
}


// height
.h15 {
  height: 15px;
}

.h30 {
  height: 30px;
}

// margin
.mt-10 {
  margin-top: 10px;
}
.mt-20 {
  margin-top: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}

.el-dialog-div {
  height: 65vh;
  overflow-y: hidden;
  overflow-x: hidden;
  
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

.card-tool {
  float: right;
  padding: 3px 0;
}