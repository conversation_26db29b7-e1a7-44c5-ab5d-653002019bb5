import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/appKeyConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/appKeyConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function getDetailByKey(appKey) {
  return request({
    url: '/appKeyConfig/getByKey',
    method: 'get',
    params: {
      appKey,
    },
  })
}

export function addAppKeyConfig(data) {
  return request({
    url: '/appKeyConfig/add',
    method: 'post',
    data: data,
  })
}

export function editAppKeyConfig(data) {
  return request({
    url: '/appKeyConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delAppKeyConfig(id) {
  return request({
    url: '/appKeyConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}
