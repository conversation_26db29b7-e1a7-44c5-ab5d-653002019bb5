<template>
  <div class="select-container">
    <el-select
      v-model="value"
      placeholder="请选择"
      class="project"
      @change="handleProjectMenuChange"
    >
      <el-option-group v-for="group in options" :key="group.label" :label="group.label">
        <el-option
          v-for="item in group.options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-option-group>
    </el-select>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
export default {
  name: 'SelectProject',
  data() {
    return {
      options: [
        {
          label: '视频小程序',
          options: [
            { value: '2-1', label: '黑豆视频' },
            { value: '2-2', label: '黑豆祝福视频' },
            { value: '2-3', label: '黑豆好看视频' },
            { value: '2-4', label: '黑豆短视频' },
            { value: '2-5', label: '扁豆视频' },
          ],
        },
        {
          label: '相册小程序',
          options: [{ value: '1-1', label: '黑豆相册' }],
        },
      ],
      value: '',
    }
  },
  computed: {
    ...mapGetters(['minaProject', 'minaType', 'projectType']),
  },
  mounted() {
    console.log('当前平台：', this.minaProject)
    // console.log('当前平台：', projectInfo.projectType[this.minaType][this.projectType])
    if (this.minaProject) {
      this.value = this.minaProject
    }
  },
  methods: {
    ...mapActions(['SwitchMinaProject']),
    handleProjectMenuChange(value) {
      console.log('切换平台：', value)
      // 判断是否是同一类型小程序
      const isSameMinaType = value.startsWith(this.minaType)
      this.SwitchMinaProject(value)
      const loading = this.$loading({
        lock: true,
        text: 'Loading...',
      })
      const self = this
      if (!isSameMinaType) {
        self.$router.replace({ path: '/' })
      }

      setTimeout(() => {
        loading.close()

        location.reload()
      }, 1000)
    },
  },
}
</script>

<style lang="scss" scoped>
.select-container {
  display: inline-block;

  .project {
    top: -15px;
  }
}
</style>
