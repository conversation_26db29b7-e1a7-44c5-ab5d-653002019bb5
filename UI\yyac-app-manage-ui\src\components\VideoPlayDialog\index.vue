<template>
  <el-dialog
    v-bind="$attrs"
    v-on="$listeners"
    @open="onOpen"
    @close="onClose"
    append-to-body
    destroy-on-close
  >
    <el-row>
      <el-col :span="24">
        <div class="video-container">
          <video v-if="isShow" class="video" :src="playUrl" autoplay controls="controls">
            您的浏览器不支持 video 标签。
          </video>
        </div>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
import jsonp from 'jsonp'
export default {
  props: {
    url: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      isShow: false,
      playUrl: '',
    }
  },
  methods: {
    async onOpen() {
      this.isShow = true
      const qqPrefixUrl = 'http://qqvid:'
      try {
        this.playUrl = null
        if (this.url.startsWith(qqPrefixUrl)) {
          this.playUrl = await this.getTXVideo(this.url.substring(qqPrefixUrl.length))
        } else {
          this.playUrl = this.url
        }
      } catch (error) {
        this.$message.error(error)
      }
    },

    onClose() {
      this.isShow = false
    },

    getTXVideo(vid) {
      return new Promise((resolve, reject) => {
        let url =
          'https://vv.video.qq.com/getinfo?vid=' + vid + '&platform=101001&charge=0&otype=json'
        jsonp(url, null, (err, data) => {
          if (err) {
            reject(`解析视频源异常[vid:${vid}]`)
          }
          const txObj = data
          if (txObj.msg == 'vid is wrong') {
            reject('vid is wrong')
            return
          }
          let u = txObj.vl.vi[0].ul.ui[0].url /*.replace(/http/, 'https')*/
          let fn = txObj.vl.vi[0].fn
          let vkey = txObj.vl.vi[0].fvkey
          let pUrl = u + fn + '?vkey=' + vkey
          console.log('pUrl: ', pUrl)
          resolve(pUrl)
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.video-container {
  height: calc(100vh - 250px);
  overflow: hidden;
}
.video {
  width: 100%;
  height: 100%;
}
</style>
