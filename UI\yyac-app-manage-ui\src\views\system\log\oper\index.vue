<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline label-width="80px">
        <el-row :gutter="15">
          <el-form-item label="标题">
            <el-input v-model="queryParams.name"></el-input>
          </el-form-item>
          <el-form-item label="操作人员">
            <el-input v-model="queryParams.operator"></el-input>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="queryParams.operateType" placeholder="">
              <el-option
                v-for="item in operateTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.date"
              type="daterange"
              placeholder=""
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="$refs.table.refresh(true)"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="() => (queryParams = {})">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center"></el-table-column>
        <el-table-column prop="logId" label="ID" align="center" width="80"></el-table-column>
        <el-table-column prop="name" label="标题" align="center" width="200"></el-table-column>
        <el-table-column prop="operateType" label="操作类型" align="center" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.operateType == operateType.Add" type="success">新增</el-tag>
            <el-tag v-if="scope.row.operateType == operateType.Edit" type="warning">修改</el-tag>
            <el-tag v-if="scope.row.operateType == operateType.Delete" type="danger">删除</el-tag>
            <el-tag v-if="scope.row.operateType == operateType.Query">查询</el-tag>
            <el-tag v-if="scope.row.operateType == operateType.Unknown" type="info">其它</el-tag>
            <el-tag v-if="scope.row.operateType == operateType.Login" color="#0070d3" effect="dark"
              >登录</el-tag
            >
            <el-tag v-if="scope.row.operateType == operateType.Logout" color="#ec0810" effect="dark"
              >注销</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column prop="tableName" label="表名" align="center" width="180">
          <template slot-scope="scope">
            <span v-if="scope.row.tableName">{{ scope.row.tableName }}</span>
            <span v-else> - </span>
          </template>
        </el-table-column>
        <el-table-column prop="tableId" label="ID" align="center" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.tableId">{{ scope.row.tableId }}</span>
            <span v-else> - </span>
          </template>
        </el-table-column>
        <el-table-column prop="desc" label="描述" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.desc">{{ scope.row.desc }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP" align="center" width="200">
          <template slot-scope="scope">
            <div>{{ scope.row.ip }}</div>
            <div>{{ scope.row.ipLocation }}</div>
          </template></el-table-column
        >
        <el-table-column prop="status" label="状态" align="center" width="100">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status == '0'">成功</el-tag>
            <el-tag type="warning" v-else-if="scope.row.status == '1'">失败</el-tag>
            <el-tag type="danger" v-else>异常</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人员" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.operatorName }}</div>
            <div>ID: {{ scope.row.operatorId }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="logTime"
          label="操作时间"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleView(scope.row)">详细</el-button>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
    <detail-dialog ref="detailDialog" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { OperateType } from '@/utils/constant'
import DetailDialog from './DetailDialog'
import * as logApi from '@/api/system/log'
export default {
  mixins: [tableHeightMixin],
  components: {
    DetailDialog,
  },
  data() {
    return {
      searchCol: {
        xs: 24,
        md: 8,
      },
      queryParams: {},
      loading: false,
      tableData: {},
      operateTypeOptions: [
        { label: '新增', value: 1 },
        { label: '修改', value: 2 },
        { label: '删除', value: 3 },
        { label: '查询', value: 4 },
        { label: '注销', value: 5 },
        { label: '登录', value: 6 },
        { label: '其它', value: 9 },
      ],
      statusOptions: [
        { label: '成功', value: '0' },
        { label: '失败', value: '1' },
        { label: '异常', value: '2' },
      ],
      operateType: OperateType,
    }
  },
  methods: {
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (params.date) {
        params.beginTime = params.date[0]
        params.endTime = params.date[1]
      }
      const res = await logApi.getOperLog(params)
      this.tableData = res
      this.$xloading.hide()
    },

    renderOperateType(row) {
      const operateType = row.operateType
      let text = '未知'
      switch (operateType) {
        case '1':
          text = '新增'
          break
        case '2':
          text = '修改'
          break
        case '3':
          text = '删除'
          break
        case '4':
          text = '查询'
          break
        case '5':
          text = '退出登录'
          break
        default:
      }

      return text
    },

    handleView(row) {
      this.$refs.detailDialog.show(row)
    },
  },
}
</script>

<style></style>
