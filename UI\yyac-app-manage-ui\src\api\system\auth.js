import request from '@/utils/request'

export function zlogin(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data,
  })
}

export function zauth(data) {
  return request({
    url: '/auth/auth',
    method: 'post',
    data,
  })
}

export function getYYAuthUrl(data) {
  return request({
    url: '/auth/yyauthUrl',
    method: 'post',
    data,
  })
}

export function yyoauth(data) {
  return request({
    url: '/auth/yyoauth',
    method: 'post',
    data,
  })
}
