import { optionsApi, appsApi } from '@/api'
import { userInfoStatus } from '@/utils/constant'

export function getUserInfoStatus() {
  return userInfoStatus
}

function getOptions(res) {
  if (res.code == 0) {
    return res.data
  }
  return []
}

// 是否可用
export const enableOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
]

// 状态
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
  { label: '屏蔽', value: 3 },
]

// 通用时间段快捷选项
const day = 60 * 1000 * 60 * 24 // 一天的毫秒数
export const commonTimePickerOptions = {
  shortcuts: [
    {
      text: '昨日',
      onClick(picker) {
        let start = new Date()
        let end = new Date()
        start.setTime(start.getTime() - day)
        end = start
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '今日',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近三日',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 2)
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近一周',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 6)
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 29)
        picker.$emit('pick', [start, end])
      },
    },
  ],
}

export const getTodayStartTimeStr = () => {
  var currentDate = new Date()
  var year = currentDate.getFullYear().toString()
  var month = (currentDate.getMonth() + 1).toString()
  if (month.length === 1) {
    month = '0' + month
  }
  var date = currentDate.getDate().toString()
  if (date.length === 1) {
    date = '0' + date
  }
  var hour = '00'
  var minute = '00'
  var second = '00'
  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}

export const getCurrentTimeStr = (addHours) => {
  var currentDate = new Date()
  var year = currentDate.getFullYear().toString()
  var month = (currentDate.getMonth() + 1).toString()
  if (month.length === 1) {
    month = '0' + month
  }
  var date = currentDate.getDate().toString()
  if (date.length === 1) {
    date = '0' + date
  }
  var hour = currentDate.getHours().toString()
  if (addHours && hour > 0) hour = parseInt(hour) + addHours
  if (hour.length === 1) {
    hour = '0' + hour
  }
  var minute = currentDate.getMinutes().toString()
  if (minute.length === 1) {
    minute = '0' + minute
  }
  var second = currentDate.getSeconds().toString()
  if (second.length === 1) {
    second = '0' + second
  }

  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}
