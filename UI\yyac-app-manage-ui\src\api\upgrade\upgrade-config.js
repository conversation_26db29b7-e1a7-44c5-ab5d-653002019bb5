import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/appUpgradeConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/appUpgradeConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addAppUpgradeConfig(data) {
  return request({
    url: '/appUpgradeConfig/add',
    method: 'post',
    data: data,
  })
}

export function editAppUpgradeConfig(data) {
  return request({
    url: '/appUpgradeConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delAppUpgradeConfig(id) {
  return request({
    url: '/appUpgradeConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}
