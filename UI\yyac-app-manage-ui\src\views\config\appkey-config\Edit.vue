<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24" v-if="isEdit">
          <el-form-item label="项目凭证" prop="appKey">
            <el-input v-model="form.appKey" placeholder="请输入项目凭证" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目名称" prop="appName">
            <el-input v-model="form.appName" placeholder="请输入App项目名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目标识" prop="appFlag">
            <el-input v-model="form.appFlag" placeholder="请输入项目标识" />
            <span class="extra">只允许输入英文、数字，一般为项目拼音首字母</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="DS标识" prop="dsProjectType">
            <el-input v-model="form.dsProjectType" placeholder="请输入DS标识" />
            <span class="extra">对应App的平台ProjectType</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="App包名" prop="appPkgName">
            <el-input v-model="form.appPkgName" placeholder="请输入App包名" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目秘钥" prop="appEncryptKey">
            <el-input v-model="form.appEncryptKey" placeholder="请输入加解密秘钥字符串" />
            <span class="extra">不填写将会自动创建</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { appKeyConfigApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      isEdit: false,
      rules: {
        appName: [{ required: true, message: '请输入App项目名称', trigger: 'blur' }],
        appFlag: [{ required: true, message: '请输入项目标识', trigger: 'blur' }],
        dsProjectType: [{ required: true, message: '请输入App标识', trigger: 'blur' }],
        //appEncryptKey: [{ required: true, message: '请输入加解密秘钥字符串', trigger: 'blur' }],
      },
    }
  },
  methods: {
    add() {
      this.isEdit = false
      this.reset()
      this.title = '添加项目'
      this.visible = true
    },
    async edit(row) {
      this.isEdit = true
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await appKeyConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await appKeyConfigApi.editAppKeyConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await appKeyConfigApi.addAppKeyConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
