<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '../mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '350px',
    },
    titleShow: {
      type: Boolean,
      default: false,
    },
    titleText: {
      type: String,
      default: '',
    },
    legentShow: {
      type: Boolean,
      default: true,
    },
    legendX: {
      type: String,
      default: 'center',
    },
    legendY: {
      type: String,
      default: 'top',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
    interval: {
      type: Number,
      default: 59,
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
        this.chart.resize()
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(expectedData) {
      this.chart.setOption(
        {
          backgroundColor: '',
          title: {
            show: this.titleShow,
            text: this.titleText,
            left: 'center',
            padding: [5, 0, 0, 15],
            //top:  '1%',
          },

          xAxis: {
            splitLine: {
              show: false, //x轴网格
              lineStyle: {
                color: '#9ea3a6',
                width: 0.5,
                type: 'solid',
              },
            },
            axisLine: {
              //x轴线的颜色以及宽度
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: this.interval,
              //y轴文字的配置
              textStyle: {
                color: '#474444',
                margin: 10,
              },
              formatter: '{value}', //y轴的每一个刻度值后面加上‘%’号
            },
            type: 'category',
            boundaryGap: false,
            data: expectedData.columns, //['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          yAxis: {
            splitLine: {
              show: true, //x轴网格
              lineStyle: {
                color: '#9ea3a6',
                width: 0.5,
              },
            },
            axisLabel: {
              //y轴文字的配置
              textStyle: {
                color: '#474444',
                margin: 10,
              },
            },
            axisLine: {
              //y轴线的颜色以及宽度
              show: false,
              lineStyle: {
                color: '#fff',
                width: 1,
                type: 'solid',
              },
            },
            //min: -25,
            //max: 50,
            scale: true,
            type: 'value',
          },
          grid: {
            left: 10,
            right: 10,
            bottom: 30,
            top: 50,
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255,255,255,0.8)',
            axisPointer: {
              type: 'line',
            },
            padding: [5, 10],
            //formatter: '{b0}<br/>{a0} {c0}%<br />{a1} {c1}%<br />{a2} {c2}%',
            formatter: function (params) {
              //console.log(params) //打印params
              let str =
                '<span style="color: #000000;font-size:xx-small;">' +
                params[0].axisValue +
                '</span><br/>'
              params.forEach((item, idx) => {
                if (item.data != null) {
                  str += `${item.marker}<span style="color: #000000;font-size:x-small;">${item.seriesName}: <b>${item.data}</b></span>`
                  str += idx === params.length - 1 ? '' : '<br/>'
                }
              })
              return str
            },
          },
          legend: {
            //orient: 'vertical',
            show: this.legentShow,
            x: this.legendX, //'center', //可设定图例在左、右、居中
            y: 'bottom', //可设定图例在上、下、居中
            data: expectedData.name, //['expected', 'actual'],
            textStyle: {
              //color: '#fff',
              padding: [0, 3], //文字与图形之间的左右间距
            },

            itemWidth: 14, //图例标记的图形宽度。
            itemHeight: 10, //图例标记的图形高度。
            itemGap: 50, //图例每项之间的间隔
          },
          series: expectedData.series,
        },
        true
      )
    },
  },
}
</script>
