<template>
  <editor id="tinymce" v-model="content" class="editor" :init="editorInit" />
</template>

<script>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/icons/default/icons.min.js'

import 'tinymce/plugins/code'
import 'tinymce/plugins/link'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/table'
import 'tinymce/plugins/image'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/preview'

import toolbar from './toolbar'
import plugins from './plugins'
import { uploadUrl } from '@/api/file'
import { getToken } from '@/utils/auth'

export default {
  components: {
    Editor,
  },
  props: {
    value: {
      type: String,
    },
    plugins: {
      type: [String, Array],
      default: () => {
        return plugins
      },
    },
    toolbar: {
      type: [String, Array],
      default: () => {
        return toolbar
      },
    },
    uploadType: {
      // 上传类型
      type: Number,
    },
  },
  mounted() {
    // console.log(this.value)
  },
  data() {
    return {
      content: this.value,
      editorInit: {
        skin_url: '/static/tinymce/skins/ui/oxide',
        content_css: '/static/tinymce/skins/content/default/content.css',
        language_url: '/static/tinymce/langs/zh_CN.js', // 语言包的路径
        language: 'zh_CN', // 语言
        height: 500, // 编辑器高度
        branding: false, // 是否禁用“Powered by TinyMCE”
        menubar: false, // 顶部菜单栏显示
        // toolbar: 'undo redo | styleselect bold italic | alignleft aligncenter alignright bullist numlist outdent indent | forecolor backcolor',
        toolbar: this.toolbar,
        plugins: this.plugins,
        images_upload_url: uploadUrl,
        image_uploadtab: true,
        // 图片上传自定义实现
        images_upload_handler: (blobInfo, success, failure) => {
          var xhr, formData

          xhr = new XMLHttpRequest()
          xhr.withCredentials = false
          xhr.open('POST', uploadUrl)
          xhr.setRequestHeader('Access-Token', getToken())

          xhr.onload = function () {
            var json
            if (xhr.status != 200) {
              failure('HTTP Error: ' + xhr.status)
              return
            }
            json = JSON.parse(xhr.responseText)
            console.log(json)
            if (!json || json.code != 0) {
              failure('上传失败: ' + xhr.responseText)
              return
            }
            success(json.data.url)
          }
          formData = new FormData()
          formData.append('uploadType', this.uploadType)
          formData.append('file', blobInfo.blob(), blobInfo.filename())
          xhr.send(formData)
        },
      },
    }
  },
  watch: {
    value(newVal) {
      // console.log('value: ', newVal)
      this.content = newVal
    },
    content(newVal) {
      this.$emit('input', newVal)
    },
  },
}
</script>

<style></style>
