<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            v-model="queryParams.appFlag"
            url="/appKeyConfig/flagOptions"
          ></x-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="安卓Id">
          <el-input v-model="queryParams.deviceId"></el-input>
        </el-form-item>
        <el-form-item label="渠道Id">
          <el-input v-model="queryParams.channelId"></el-input>
        </el-form-item>
        <el-form-item label="锚点">
          <x-select
            v-model="queryParams.symbol"
            :options="symbolOptions"
            url="/symbolConfig/options"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column
          prop="symbol"
          label="锚点标识"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="symbolName"
          label="锚点名称"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="value"
          label="锚点值"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="channelId"
          label="渠道Id"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="packCId"
          label="包渠道Id"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="channelName"
          label="渠道名称"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="appFlag"
          label="App标识"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="deviceId"
          label="安卓Id"
          align="center"
          width="200"
        ></el-table-column>
        <el-table-column prop="brand" label="品牌" align="center" min-width="100"></el-table-column>
        <el-table-column prop="model" label="型号" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="screen"
          label="屏幕"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="createOn" label="时间" align="center" width="180"></el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { symbolRunLogApi } from '@/api'
export default {
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        appFlag: -1,
        runType: -1,
        date: '',
      },
      loading: false,
      tableData: {},
      symbolOptions: [],
    }
  },
  created() {
    this.reset()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appFlag: -1,
        runType: -1,
        date: dayjs().format('YYYY-MM-DD'),
      }
      this.$refs.table.refresh(true)
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = beginDate
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date
      }
      const res = await symbolRunLogApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
  },
}
</script>
