<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="标题" prop="adTitle">
            <el-input v-model="form.adTitle" placeholder="" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="checkbox" prop="proj" >
            <el-checkbox-group v-model="form.proj">
              <el-checkbox v-for="item in projOptions" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="radio" prop="minaId" >
            <el-radio-group v-model="form.minaId">
              <el-radio v-for="item in minaOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="权重" prop="prop">
            <el-input-number
              v-model="form.prop"
              controls-position="right"
              :min="1"
            ></el-input-number>
            <span class="extra">权重越大，素材展示的机率越大</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import {} from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
    }
  },

  mounted() {},

  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await api.XXX(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await api.XXX(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await api.XXX(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
