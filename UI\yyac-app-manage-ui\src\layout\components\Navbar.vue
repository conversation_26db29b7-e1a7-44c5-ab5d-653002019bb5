<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" />

    <!-- 右侧下拉菜单、头像 -->
    <div class="right-menu">
      <!-- 搜索菜单 -->
      <search />

      <el-dropdown class="dropdown" trigger="click">
        <span class="el-dropdown-link">
          {{ name }}<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/system/account-setting">
            <el-dropdown-item>
              账号设置
            </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="avatar-container">
        <el-avatar :size="40" :src="avatarUrl"></el-avatar>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Search from './Search'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import avatarPlaceholder from '@/assets/images/avatar_placeholder.png'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Search,
  },
  data() {
    return {
      avatarUrl: avatarPlaceholder,
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device', 'name']),
  },
  mounted() {
    if (this.avatar) {
      this.avatarUrl = this.avatar
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$xloading.show()
      await this.$store.dispatch('Logout')
      this.$xloading.hide()
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    // background-color: brown;
    line-height: 50px;

    .avatar-container {
      // background-color: aqua;
      height: 100%;
      display: inline-block;
      width: 60px;
      text-align: center;
      padding-top: 5px;
    }

    .dropdown {
      top: -16px;
    }
  }
}
</style>
