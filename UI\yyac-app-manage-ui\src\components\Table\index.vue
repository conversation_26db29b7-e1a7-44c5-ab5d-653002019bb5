<!--
--- 使用vue来实现xtable ---
-->
<template>
  <div>
    <el-table
      v-bind="tableProps"
      v-on="$listeners"
    >
      <!-- 动态插槽内容 -->
      <slot v-for="(slotContent, name) in $slots" :name="name" :key="name">
        <!-- 如果需要，可以在这里提供默认内容 -->
      </slot>
    </el-table>

    <!-- 分页组件，基于 `page` 属性条件渲染 -->
    <el-pagination
      v-if="page"
      background
      :layout="pageLayout"
      :page-sizes="pageSizes"
      :total="data.total"
      :page-size="pageSize"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @prev-click="handlePrevClick"
      @next-click="handleNextClick"
    />

    <!-- 回到顶部按钮 -->
    <el-backtop />
  </div>
</template>

<script>
import { Table, Message } from 'element-ui'

export default {
  name: 'PaginatedTable',
  props: {
    // 继承自 Element UI 的 Table 组件的所有 props
    ...Table.props,
    // 额外的自定义 props
    page: {
      type: Boolean,
      default: true,
    },
    //data: {
    //  type: Object,
    //  required: true,
    //},
    autoLoad: {
      type: Boolean,
      default: true,
    },
    loadData: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 50,
      pageSizes: [20, 50, 100, 200, 500],
      pageLayout: 'total, sizes, prev, pager, next, jumper',
    }
  },
  computed: {
    // 计算传递给 el-table 的 props
    tableProps() {
      //return Object.keys(Table.props).reduce((acc, key) => {
      //  if (this[key]) {
      //    acc[key] = key === 'data' ? this[key].data : this[key]
      //  }
      //  return acc
      //}, {})
      return Table.props
    },
  },
  created() {
    // 自动加载数据
    if (this.autoLoad) {
      this.loadTableData(this.currentPage, this.pageSize)
    }
  },
  watch: {
    data(newData) {
      if (newData && newData.code !== 0) {
        Message({
          type: 'error',
          message: newData.msg || '请求出错',
        })
      }
    },
  },
  methods: {
    updatePageUI(page, size) {
      this.currentPage = page
      this.pageSize = size
    },
    refresh(reload = false) {
      if (reload) {
        this.currentPage = 1
      }
      this.loadTableData()
    },
    loadTableData() {
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
      }
      // 调用传入的 loadData 方法，确保其返回 Promise 以便处理异步逻辑
      this.loadData(params).catch((error) => {
        Message({
          type: 'error',
          message: error.message || '数据加载失败',
        })
      })
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize
      this.loadTableData()
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage
      this.loadTableData()
    },
    handlePrevClick() {
      this.loadTableData()
    },
    handleNextClick() {
      this.loadTableData()
    },
  },
}
</script>

<style scoped>
/* 根据需要添加样式 */
</style>
