<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['appUpgradeConfig:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="title" label="标题" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="verCode"
          label="版本号"
          align="center"
          min-width="80"
        ></el-table-column>
        <el-table-column label="升级权重" align="center" min-width="90">
          <template v-slot="{ row }">
            <span>{{ row.upgradeWeight }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="URL" align="center" min-width="350">
          <template v-slot="{ row }">
            <a :href="row.packURL_Url">{{ row.packURL }}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="packMD5"
          label="MD5"
          align="center"
          min-width="300"
        ></el-table-column>
        <el-table-column label="强制更新" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" v-if="row.force">是</el-tag>
            <el-tag type="danger" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启用更新" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" v-if="row.enable">是</el-tag>
            <el-tag type="danger" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启用测试" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" v-if="row.isTest">是</el-tag>
            <el-tag type="danger" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="跳转App" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" v-if="row.isJump">是</el-tag>
            <el-tag type="danger" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          label="添加时间"
          align="center"
          min-width="180"
        ></el-table-column>
        <el-table-column label="操作" min-width="250" align="center">
          <template v-slot="{ row }">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit"
              @click="handleEdit(row)"
              v-permission="['appUpgradeConfig:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              v-permission="['appUpgradeConfig:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { appUpgradeConfigApi, appKeyConfigApi } from '@/api'
import EditDialog from './edit'

export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      tableData: {},
      loading: false,
      keyConfig: {},
    }
  },
  async created() {
    let query = this.$router.history.current.query
    if (!query.appKey) {
      this.$xMsgError('缺少参数[appKey]')
      return
    }
    this.queryParams.appKey = query.appKey
    const keyConfig = await this.loadKeyConfig(query.appKey)
    this.$nextTick(() => {
      this.keyConfig = keyConfig
    })
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await appUpgradeConfigApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add(this.keyConfig)
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row, this.keyConfig)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await appUpgradeConfigApi.delAppUpgradeConfig(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    async loadKeyConfig(appKey) {
      const res = await appKeyConfigApi.getDetailByKey(appKey)
      return res.data
    },
  },
}
</script>
