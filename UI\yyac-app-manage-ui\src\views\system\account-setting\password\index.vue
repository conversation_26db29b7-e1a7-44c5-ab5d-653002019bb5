<template>
  <div class="" v-loading="loading">
    <div class="page-title">修改密码</div>
    <el-row>
      <el-col :span="24" :offset="2">
        <el-form
          ref="form"
          :model="form"
          :rules="formRules"
          label-width="100px"
          inline
          size="small"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="旧密码" prop="oldPassword">
                <el-input
                  v-model.trim="form.oldPassword"
                  placeholder=""
                  show-password
                  autocomplete="new-password"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="新密码" prop="password">
                <el-input v-model.trim="form.password" placeholder="" show-password />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="确认新密码" prop="confirmPassword">
                <el-input v-model.trim="form.confirmPassword" placeholder="" show-password />
              </el-form-item>
            </el-col>
            <el-col :span="8" :offset="2">
              <el-form-item>
                <el-button type="primary" @click="submitForm">保存</el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { encryptRSA } from '@/utils/encrypt'
import { sysUserApi } from '@/api'
export default {
  data() {
    var checkConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      form: {},
      formRules: {
        oldPassword: [{ required: true, message: '不能为空', trigger: 'blur' }],
        password: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { min: 6, max: 16, message: '长度在 6 到 16 个字符' },
        ],
        confirmPassword: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { min: 6, max: 16, message: '长度在 6 到 16 个字符' },
          { validator: checkConfirmPassword },
        ],
      },
    }
  },
  methods: {
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          data.oldPassword = encryptRSA(data.oldPassword)
          data.password = encryptRSA(data.password)
          data.confirmPassword = encryptRSA(data.confirmPassword)
          const res = await sysUserApi.changePassword(data)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('修改成功')
            this.visible = false
            this.resetForm()
            await this.$store.dispatch('Logout')
            setTimeout(() => {
              this.$router.push(`/login`)
            }, 1500)
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        }
      })
    },
    resetForm() {
      this.$xResetForm('form')
    },
  },
}
</script>

<style lang="scss" scoped>
.page-title {
  margin-left: 40px;
  margin-bottom: 30px;
  font-size: 20px;
  line-height: 28px;
}
</style>
