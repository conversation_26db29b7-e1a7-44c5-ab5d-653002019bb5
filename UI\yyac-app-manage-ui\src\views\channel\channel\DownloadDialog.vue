<template>
    <x-dialog :title="title" :visible.sync="visible" :show-footer="true" @cancel="close">
      <el-form ref="form" :model="form" label-width="150px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属项目：">
              {{ channel.appName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="渠道名称：">
              {{ channel.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="渠道Id：">
              {{ channel.id }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="下载链接：">
              <el-link :href="info.downloadUrl" type="primary" :underline="false" download>{{
                info.downloadUrl
              }}</el-link>
              <!--<span class="extra line">点击下载链接下载apk，链接有效时间3分钟</span>-->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="扫码下载：">
              <vue-qr :text="info.downloadUrl"></vue-qr>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button size="medium" @click="close">关闭</el-button>
      </template>
    </x-dialog>
  </template>
  
  <script>
  import vueQr from 'vue-qr'
  export default {
    components: {
      vueQr,
    },
    data() {
      return {
        title: '',
        visible: false,
        loading: false,
        form: {},
        channel: {},
        info: {},
        downloadStatus: undefined,
        downloadPercentage: 0,
        showProgress: false,
      }
    },
    methods: {
      show(channel, info) {
        this.reset()
        this.channel = channel
        this.info = info
        this.title = '下载渠道包'
        this.visible = true
      },
  
      close() {
        this.reset()
        this.visible = false
      },
  
      reset() {
        this.form = {}
        this.$xResetForm('form')
        this.loading = false
        this.showProgress = false
      },
    },
  }
  </script>
  
  <style></style>
  