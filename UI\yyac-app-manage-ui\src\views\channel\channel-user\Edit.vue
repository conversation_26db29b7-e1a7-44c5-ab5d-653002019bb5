
<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close">

    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
                  <el-input :readonly="true" v-model="form.id" placeholder="请输入Id" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号" prop="userName">
                  <el-input v-model="form.userName" placeholder="请输入账号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="名称" prop="nickName">
                  <el-input v-model="form.nickName" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
                  <el-input v-model="form.password" placeholder="请输入密码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣量率" prop="lossRate">
                  <el-input v-model="form.lossRate" placeholder="请输入扣量率" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="账号渠道" prop="channelIdList">
            <x-checkbox
              v-model="form.channelIdList"
              url="/channel/options"
            ></x-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="enable">
            <el-radio-group v-model="form.enable">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

  </x-dialog>
</template>

<script>
import { channeluserApi } from '@/api'
import { encryptRSA } from '@/utils/encrypt'
export default {
  data () {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        statue: true,
        channelIdList: []
      },
      channelOptions: [],
    }
  },
  methods:{
    add () {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit (row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await channeluserApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset () {
      this.form = {
        statue: true,
        channelIdList:[]
      }
      this.$xResetForm('form')
    },

    submitForm () {
      this.$refs['form'].validate(async valid => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          //data.password = encryptRSA(data.password)
          if (id != undefined) {
            const res = await channeluserApi.editchannelUser(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await channeluserApi.addchannelUser(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    }
  }
}
</script>

<style>

</style>
