<template>
  <el-table-column :label="labelText" align="center" min-width="130">
    <template slot-scope="{ row }">
      <template v-if="isDayShow(row, day)">
        <div>{{ row[propName] }} / {{ row[`day${day}_Percent`] }}%</div>
      </template>
    </template>
  </el-table-column>
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: {
    day: {
      type: Number,
    },
  },
  computed: {
    propName: function () {
      return `day${this.day}`
    },
    labelText: function () {
      return this.day + '天后'
    },
  },
  methods: {
    isDayShow(row, day) {
      let date = dayjs(row.date)
      let afterDate = date.add(day, 'day')
      let today = dayjs()
      return !afterDate.isAfter(today, 'day')
    },
  },
}
</script>

<style></style>
