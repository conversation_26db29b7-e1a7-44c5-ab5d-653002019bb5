import Vue from 'vue'
import Router from 'vue-router'
import settings from '@/settings'
import qs from 'qs'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/yyoauth',
    component: () => import('@/views/auth/yyoauth'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  
  // 这个不是根节点，所有后面的每个目录都需要带component = 'layout'
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: 'index',
        name: '首页',
        component: () => import('@/views/index'),
        meta: { title: '首页', icon: 'dashboard' },
      },
    ],
  },
]

// 重写路由的push方法
// const routerPush = Router.prototype.push
// Router.prototype.push = (location) => {
//   return routerPush.call(this, location).catch(error => error)
// }

const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    base: settings.publicPath,
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
    parseQuery(query) {
      return qs.parse(query)
    },
    stringifyQuery(query) {
      // console.log('stringifyQuery:', query)
      let result = qs.stringify(query)
      // console.log(result)
      return result ? `?${result}` : ''
    },
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
