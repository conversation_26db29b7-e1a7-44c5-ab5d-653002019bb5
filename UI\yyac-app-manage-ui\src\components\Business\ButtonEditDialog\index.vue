<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="onClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input
              type="text"
              v-model="form.title"
              placeholder="请输入标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图标" prop="icon">
            <x-upload
              :upload-type="uploadType"
              :can-choose="true"
              v-model="form.icon"
              :src="form.iconUrl"
            ></x-upload>
            <span class="extra">上传图片，或填入图片完整链接</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="提示" prop="tips">
            <el-input
              type="textarea"
              v-model="form.tips"
              placeholder="请输入提示"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否有小红点" prop="hasNew">
            <el-radio-group v-model="form.hasNew">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              placeholder=""
              :min="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="Uri" prop="uri">
            <el-input v-model="form.uri" placeholder="请输入Uri"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import XUpload from '@/components/XUpload'
import { buttonApi, mainConfigApi, homeConfigApi } from '@/api'
import { UPLOAD_TYPE } from '@/utils/constant'

export default {
  components: {
    XUpload,
  },
  props: {
    type: {
      type: String, // 按钮类型，mainConfig: 首页配置按钮，
    },
  },
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
        ],
        tips: [{ max: 50, message: '长度不能超过50个字符', trigger: 'blur' }],
        icon: [{ required: true, message: '图标不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
        hasNew: [{ required: true, message: '请选择是否有小红点', trigger: 'blur' }],
      },
      uploadType: UPLOAD_TYPE.appButton,
      extraData: {},
    }
  },

  mounted() {},

  methods: {
    add(extraData) {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.extraData = extraData
    },
    async edit(row, extraData) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await buttonApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
      this.extraData = extraData
    },

    onClose() {
      this.form.icon = undefined
      this.form.iconUrl = undefined
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    // 提交的时候注意，要根据不同的按钮类型给不同的api提交
    submitForm() {
      if (!this.type) {
        console.error('请配置按钮所属的类型（参数type）')
        return
      }
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await this.editByType(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await this.addByType(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
    async addByType(data) {
      if (this.type == 'mainConfig') {
        return mainConfigApi.editMainConfig('button', { button: data })
      } else if (this.type == 'homeConfig') {
        return homeConfigApi.editHomeConfig(
          'button',
          this.getHomeConfigParamsByAction(this.extraData, data)
        )
      }
    },
    async editByType(data) {
      if (this.type == 'mainConfig') {
        return mainConfigApi.editMainConfig('button', { button: data })
      } else if (this.type == 'homeConfig') {
        return homeConfigApi.editHomeConfig(
          'button',
          this.getHomeConfigParamsByAction(this.extraData, data)
        )
      }
    },
    getHomeConfigParamsByAction(extraData, data) {
      let field = this.extraData.buttonField
      if (field == 'HeaderButtonsIds') {
        return { headerButton: data, ...this.extraData }
      } else if (field == 'ItemsButtonsIds') {
        return { itemsButton: data, ...this.extraData }
      }
    },
  },
}
</script>

<style></style>
