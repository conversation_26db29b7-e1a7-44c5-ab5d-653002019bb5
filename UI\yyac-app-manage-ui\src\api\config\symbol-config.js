import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/symbolConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/symbolConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addsymbolConfig(data) {
  return request({
    url: '/symbolConfig/add',
    method: 'post',
    data: data,
  })
}

export function editsymbolConfig(data) {
  return request({
    url: '/symbolConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delsymbolConfig(id) {
  return request({
    url: '/symbolConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}
