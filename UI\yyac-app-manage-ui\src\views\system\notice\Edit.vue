<template>
  <x-dialog
    v-loading="loading"
    :title="title"
    :visible.sync="visible"
    width="1000px"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入通知标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布时间" prop="createOn">
            <el-date-picker
              v-model="form.createOn"
              placeholder="请选择发布时间"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
            <span class="extra line">发布时间非必填，不填则默认当前时间</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否置顶" prop="isTop">
            <el-input-number v-model="form.isTop" controls-position="right" />
            <span class="extra line">数值越大，排名置顶越前</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <tinymce v-model="form.content" :upload-type="uploadType" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import { systemNoticeApi } from '@/api'
import { UPLOAD_TYPE } from '@/utils/constant'

export default {
  components: {
    Tinymce,
  },
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
      },
      uploadType: UPLOAD_TYPE.appSystemNotice,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await systemNoticeApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.visible = false
    },

    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        content: '',
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await systemNoticeApi.editSystemNotice(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await systemNoticeApi.addSystemNotice(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>
