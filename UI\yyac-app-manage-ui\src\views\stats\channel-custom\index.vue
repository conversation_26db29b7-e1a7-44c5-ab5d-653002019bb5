<template>
  <div class="app-container">
    <el-card>
      <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            v-model="queryParams.appId"
            url="/appKeyConfig/options"
            @change="handleAppIdChange"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select
            show-default
            v-model="queryParams.channelId"
            :options="channelOptions"
            url="/channel/options"
          />
        </el-form-item>
        <el-form-item label="锚点">
          <x-select
            v-model="queryParams.symbol"
            :options="symbolOptions"
            url="/symbolConfig/options"
          />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" width="150"></el-table-column>
        <el-table-column
          prop="symbol"
          label="锚点标识"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="symbolName"
          label="锚点名称"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="value" label="数值" align="center" min-width="100"
          ><template #header>
            <x-table-header-tip
              label="数值"
              tip="按锚点类型不同数值标识的数据也不一样"
            ></x-table-header-tip> </template
        ></el-table-column>
        <el-table-column
          prop="count"
          label="锚点总数量"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="valueNew"
          label="新用户数值"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="countNew"
          label="新用户锚点数量"
          align="center"
          min-width="100"
        ></el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { statsApi, channelApi } from '@/api'
export default {
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        appId: -1,
        channelId: -1,
        symbol: '',
      },
      loading: false,
      tableData: {},
      channelOptions: [],
      symbolOptions: [],
    }
  },
  created() {
    this.reset()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appId: -1,
        channelId: -1,
        symbol: '',
      }
      this.reset()
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await statsApi.getChannelCustomStatsList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD')
      const endDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = [beginDate, endDate]
    },
    async handleAppIdChange() {
      this.queryParams.channelId = -1
      const res = await channelApi.getOptions({ appId: this.queryParams.appId })
      if (res.code == 0) {
        this.channelOptions = res.data
      }
    },
  },
}
</script>

<style></style>
,
