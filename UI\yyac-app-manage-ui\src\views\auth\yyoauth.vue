<template>
  <div class="app-container" align="center" v-loading="loading">
    <h2>{{ text }}</h2>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
export default {
  data() {
    return {
      text: '登录中...',
      loading: false,
    }
  },
  mounted() {
    const query = this.$router.history.current.query
    if (!query.code) {
      this.$xMsgError('无效的code，请重试')
      this.$router.replace({ path: '/login' })
      return
    }
    // 获取token
    this.getToken(query)
  },
  methods: {
    ...mapActions(['OAuthLogin']),
    async getToken(data) {
      this.$xloading.show()
      try {
        await this.OAuthLogin(data).then((res) => {
          //onsole.log(res)
          // 跳转到首页
          this.$router.replace({ path: '/' })
        })
        this.$xloading.hide()
      } catch (error) {
        // 登录失败
        this.$xMsgError('登录失败，' + error)
        this.$router.replace({ path: '/login' })
        this.$xloading.hide()
      }
    },
  },
}
</script>

<style></style>
