<template>
  <div class="app-container">
    <el-card>
      <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            v-model="queryParams.appId"
            url="/appKeyConfig/options"
            @change="handleAppIdChange"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select
            show-default
            v-model="queryParams.channelId"
            :options="channelOptions"
            url="/channel/options"
          />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        :cell-style="handleCellStyle"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" width="150"></el-table-column>
        <el-table-column
          prop="day0"
          label="排重安装数"
          align="center"
          min-width="100"
        ></el-table-column>
        <day-table-column :day="1" />
        <day-table-column :day="2" />
        <day-table-column :day="3" />
        <day-table-column :day="4" />
        <day-table-column :day="5" />
        <day-table-column :day="6" />
        <day-table-column :day="7" />
      </x-table>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { statsApi, channelApi } from '@/api'
import DayTableColumn from './DayTableColumn'

export default {
  components: {
    DayTableColumn,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        appId: -1,
        channelId: -1,
      },
      loading: false,
      tableData: {},
      channelOptions: [],
    }
  },
  created() {
    this.reset()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appId: -1,
        channelId: -1,
      }
      this.reset()
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await statsApi.getChannelRetainList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    reset() {
      const beginDate = dayjs().subtract(10, 'day').format('YYYY-MM-DD')
      const endDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = [beginDate, endDate]
    },
    // eslint-disable-next-line no-unused-vars
    handleCellStyle({ row, column, rowIndex, columnIndex }) {
      // 判断是否显示当前cell
      let columnName = column.label
      let reg = /^[1-9]+/g
      let matchs = columnName.match(reg)
      // console.log(matchs)
      if (!matchs) {
        return
      }
      let day = matchs[0]
      let isShow = this.isDayShow(row, day)
      if (!isShow) {
        return
      }
      // 不同百分比显示不同的背景颜色
      let bgColor = null
      let percent = row[`day${day}_Percent`]
      if (percent == 0) {
        return
      }
      // 留存率越大背景色越深
      if (percent < 10) {
        bgColor = '#e6fcf5'
      } else if (percent < 20) {
        bgColor = '#c3fae8'
      } else if (percent < 30) {
        bgColor = '#96f2d7'
      } else if (percent < 50) {
        bgColor = '#63e6be'
      } else if (percent < 60) {
        bgColor = '#38d9a9'
      } else {
        bgColor = '#20c997'
      }
      if (bgColor) {
        return `background: ${bgColor};`
      }
    },
    isDayShow(row, day) {
      let date = dayjs(row.date)
      let afterDate = date.add(day, 'day')
      let today = dayjs()
      return !afterDate.isAfter(today, 'day')
    },

    async handleAppIdChange() {
      this.queryParams.channelId = -1
      const res = await channelApi.getOptions({ appId: this.queryParams.appId })
      if (res.code == 0) {
        this.channelOptions = res.data
      }
    },
  },
}
</script>

<style></style>
