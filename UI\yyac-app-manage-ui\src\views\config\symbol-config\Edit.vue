<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
            <el-input :readonly="true" v-model="form.id" placeholder="请输入Id" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="锚点标识" prop="symbol">
            <el-input v-model="form.symbol" placeholder="请输入锚点标识" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="锚点名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入锚点名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值类型" prop="type">
            <x-select v-model="form.type" url="/options/getSymbolTypes"></x-select>
            <!-- <el-input v-model="form.appFlag" placeholder="请输入项目标识，只允许填写英文字母" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="dest">
            <el-input type="textarea" rows="5" v-model="form.dest" placeholder="请输入描述" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { symbolConfigApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        symbol: [{ required: true, message: '请输入锚点标识', trigger: 'blur' }],
        name: [{ required: true, message: '请输入锚点名称', trigger: 'blur' }],
      },
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await symbolConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await symbolConfigApi.editsymbolConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await symbolConfigApi.addsymbolConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
