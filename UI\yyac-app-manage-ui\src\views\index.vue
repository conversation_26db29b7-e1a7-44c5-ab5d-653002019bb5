<template>
  <div class="app-container">
    <el-row> </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  data() {
    return {
      cardCol: {
        xs: { span: 24 },
        sm: { span: 12 },
        md: { span: 8 },
      },
      quickStartCol: {
        xs: { span: 12 },
        sm: { span: 8 },
        xl: { span: 6 },
      },
    }
  },
  computed: {
    ...mapGetters(['name', 'minaType']),
  },
  methods: {
    quickStart(item) {
      if (item.href) {
        this.$router.push({ path: item.href })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.quick-start {
  display: inline-block;
  margin: 10px;
}

.quick-crad {
  margin: 10px;
}
</style>
