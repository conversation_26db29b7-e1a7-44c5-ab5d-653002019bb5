<template>
  <div class="app-container">
    <el-card>
      <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            v-model="queryParams.appId"
            url="/appKeyConfig/options"
            @change="handleAppIdChange"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select
            show-default
            v-model="queryParams.channelId"
            :options="channelOptions"
            url="/channel/options"
          />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" width="150"></el-table-column>
        <el-table-column
          prop="install"
          label="排重安装数"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="installNoCheck" label="真实安装" align="center" min-width="150">
          <template #header>
            <x-table-header-tip label="真实安装数" tip="不排重安装"></x-table-header-tip>
          </template>
        </el-table-column>
        <el-table-column
          prop="launchUserOld"
          label="留存启动用户数"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="activeUserCount" label="活跃用户数" align="center" min-width="150">
          <template #header>
            <x-table-header-tip
              label="活跃用户数"
              tip="活跃用户数=排重安装数+留存启动用户数"
            ></x-table-header-tip>
          </template>
        </el-table-column>
        <el-table-column prop="launchNew" label="新用户启动次数" align="center" min-width="150">
          <template #header>
            <x-table-header-tip
              label="新用户启动次数"
              tip="当天安装的用户启动的总次数"
            ></x-table-header-tip>
          </template>
        </el-table-column>
        <el-table-column
          prop="launchOld"
          label="留存启动次数"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="exitCount"
          label="退出次数"
          align="center"
          min-width="150"
        ></el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { statsApi, channelApi } from '@/api'
export default {
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        appId: -1,
        channelId: -1,
      },
      loading: false,
      tableData: {},
      channelOptions: [],
    }
  },
  created() {
    this.reset()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appId: -1,
        channelId: -1,
      }
      this.reset()
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await statsApi.getChannelStatsList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    reset() {
      const beginDate = dayjs().subtract(10, 'day').format('YYYY-MM-DD')
      const endDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = [beginDate, endDate]
    },
    async handleAppIdChange() {
      this.queryParams.channelId = -1
      const res = await channelApi.getOptions({ appId: this.queryParams.appId })
      if (res.code == 0) {
        this.channelOptions = res.data
      }
    },
  },
}
</script>

<style></style>
