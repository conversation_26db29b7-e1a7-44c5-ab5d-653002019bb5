import { constantRoutes } from '@/router'
import { getRouters } from '@/api/system/login'
import Layout from '@/layout/index'

const permission = {
  state: {
    routers: [],
    addRouters: [],
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRoutes.concat(routers)
    },
  },
  actions: {
    GenerateRoutes({ commit }) {
      return new Promise(async (resolve) => {
        const res = await getRouters()
        // console.log('res:', res)
        const accessedRoutes = filterAsyncRouter(res.data)
        accessedRoutes.push({ path: '*', redirect: '/404', hidden: true })
        commit('SET_ROUTERS', accessedRoutes)
        // console.log('accessedRoutes:', accessedRoutes)
        resolve(accessedRoutes)
      })
    },
  },
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, parentPath) {
  return asyncRouterMap.filter((route) => {
    // console.log('path:', route.path)
    if (!route.path) {
      return false
    }
    // if (route.path.indexOf('/') == 0) {
    //   route.path = route.path.substring(1)
    // }
    // if (parentPath) {
    //   route.name = `${parentPath}/${route.path}`
    // } else {
    //   route.name = route.path
    // }

    // console.log(route.name)
    if (route.component) {
      // Layout组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route.path)
    }
    return true
  })
}

export const loadView = (view) => {
  // 路由懒加载
  // 老外修改了webpack打包逻辑,webpack4中动态import不支持变量方式
  // return () => import(`@/views/${view}`)
  return (resolve) => require([`@/views/${view}`], resolve)
}

export default permission
