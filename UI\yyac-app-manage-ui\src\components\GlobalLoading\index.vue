<template>
  <div class="GlobalLoadingPlane" v-if="visible">
    <i style="font-size: 36px" class="el-icon-loading GlobalLoadingSpin"></i>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tip: '请等待...',
      visible: false,
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
  },
}
</script>

<style scoped>
.GlobalLoadingPlane {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000000;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0.7;
  overflow: hidden;
}

.GlobalLoadingSpin {
  position: fixed;
  top: 50%;
  left: 50%;
}
</style>
